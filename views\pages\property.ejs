<%- include ('../template/header') -%>
    <%- include ('../template/navbar') -%>
        <div class="container-fluid container-fluid-width mt-3">
            <div class="row">
                <div class="col-6">
                    <h2 class="heading-bold m-0">Property</h2>
                </div>
                <div class="col-6">
                    <% if(permission[0].create==='Y' ){ %>
                    <div class="form-group m-0 text-right">
                        <a href="<%- baseUrl -%>property/create -%>" class="btn small-btn scan-btn passport-scan-bg-green passport-scan-text-color-white"><i class="fa fa-plus"></i> create</a>
                    </div>
                    <% } %>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width mt-3 mb-3">
           <div class="row">
                <div class="col-md-12 passport-scan-bg-white br-15 responsive-col-12 p-4">
                   <div class="row">
                        <div class="col-md-12 d-flex justify-content-end mb-2">
                            <div class="form-group mr-2">
                                <select id="show-record" class="form-control no-ob">
                                    <option value="10">10</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="search" id="search" class="form-control no-ob" placeholder="Search">
                            </div>
                            <div class="form-group ml-3">
                                <button type="button"
                                    class="btn scan-btn small-btn btn-search passport-scan-bg-green passport-scan-text-color-white">Search</button>
                            </div>
                        </div>
                   </div>
                   <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th data-field="s_no" data-sortable="false">#</th>
                                            <th data-field="property_type" data-sortable="true">Property type</th>
                                            <th data-field="name" data-sortable="true">Name</th>
                                            <th data-field="contact_number" data-sortable="true">Contact number</th>
                                            <th data-field="logo" data-sortable="false">Logo</th>
                                            <th data-field="manager_name" data-sortable="true">Manager name</th>
                                            <th data-field="mobile_number" data-sortable="true">Mobile no</th>
                                            <th data-field="is_active" data-sortable="false">Status</th>
                                            <th data-field="action" data-sortable="false">Action</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                   </div>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width">
            <div class="row">
                <div class="col-md-12">
                    <div class="loadPagination row  mt-4 mb-5"></div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            var pagePermission = JSON.parse(`<%- JSON.stringify(permission[0]) -%>`);
            var url = new URL(window.location.href);
            var searchParams = new URLSearchParams(url.search);
            $(document).ready(function () {
                searchParams.get('per-page') ? $('#show-record').val(searchParams.get('per-page')) : '';
                getList();
                $('.btn-search').click(function () {
                    getList();
                });
                $('#show-record').change(function () {
                    window.location.href = `${baseUrl}property?per-page=${$('option:selected', this).val()}`;
                    getList();
                });
            });
            function getList() {
                var page = searchParams.get('page');
                var pageId = page ? `&page=${page}` : `&page=1`;
                var beforeQ = page ? '&' : '&';
                var search = $('#search').val() !== "" ? (beforeQ + encodeURI(`search=${$('#search').val()}`)) : '';

                $.ajax({
                    url: `${apiUrl}property?total-page=${$('#show-record option:selected').val()}${pageId}${search}`,
                    method: 'get',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    },
                    success: function (res) {
                         if (res.status === true) {
                            var myData = [];
                            $.each(res.data, function (ind, row) {
                                var _editBtn = pagePermission.edit ==='Y' ? `<a class="mr-2" href="<%- baseUrl -%>property/edit/${row.property_id}"><i class="fa fa-pencil"></i></a>`:'';
                                var _deleteBtn = pagePermission.edit ==='Y' ?`<a class="text-danger" href="javascript:;" onclick="deleteProperty(${row.property_id})"><i class="fa fa-trash"></i></a>`:'';
                                myData.push({
                                    "s_no": (parseInt(res.offset) + parseInt(ind + 1)),
                                    "property_type": row.property_type_name,
                                    "name": row.name,
                                    "contact_number": '+'+row.contact_number,
                                   "logo": row.logo ? `<img style="width:80px; height:40px;" src="<%- baseUrl -%>uploads/${row.logo}" />`:`<img style="width:80px; height:40px;" src="<%- baseUrl -%>assets/img/property-logo.png" />`,
                                    "manager_name": row.manager_name,
                                    "mobile_number": '+'+row.mobile_number,
                                    "is_active":row.is_active ==='A'?'<span class="badge badge-success text-small">Active</span>' : '<span class="badge badge-secondary text-small">Deactive</span>',
                                    "action": `${_editBtn} ${_deleteBtn}`
                                });
                            });
                            $('table').bootstrapTable('destroy');
                            $('table').bootstrapTable({
                                data: myData
                            });
                            bindPagination(page, res.totalPages,'property');
                        }
                    }
                });
            }
            function deleteProperty(id) {
                cuteAlert({
                    type: "question",
                    title: "Delete the property",
                    message: "Are you sure do you want to delete?",
                    confirmText: "Yes",
                    cancelText: "No"
                  }).then((e)=>{
                    if ( e == 'confirm'){
                        $.ajax({
                            url: `${apiUrl}property/${id}`,
                            type: 'delete',
                            dataType: 'json',
                            headers: {
                                "Authorization": "Bearer <%- userDetails.token -%>"
                            },success: function (res) {                               
                                cuteAlert({
                                    type: res.status?"success":"error",
                                    title: "Delete property",
                                    message: res.msg,
                                    buttonText: "Ok"
                                  }).then(()=>{
                                    res.status ? getList() : null;
                                  })
                            }, complete: function () {

                            }
                        })
                  }
                  })

            }

            function bindPagination(page, totalRow, url) {
  var firstBtnState = page <= 1 ? 'disabled' : '';
  var previousBtnState = page <= 1 ? 'javascript:;' : baseUrl + `${url}?page=` + (parseInt(page) - 1) + `&per-page=${$('#show-record option:selected').val()}`;
  var disabledFirstLi = page == 1 ? 'disabled' : '';
  var pagination = `<div class="col-12">
                  <ul class="pagination justify-content-end">
                      <li class="page-item ${disabledFirstLi}"><a href="${baseUrl}${url}?page=1&per-page=${$('#show-record option:selected').val()}" class="page-link">First</a></li>
                      <li class="${firstBtnState} page-item">
                          <a href="${previousBtnState}" class="page-link">Prev</a>
                      </li>`;

  var disabledLi = 1 <= totalRow ? 'disabled' : '';
  var nextPageCount = page >= totalRow ? parseInt(page) : (parseInt(page ? page : 1) + 1);
  var disabledLastLi = page == totalRow ? 'disabled' : '';

  pagination += ` <li class="${disabledLi} page-item"><a class="page-link" href="${baseUrl}${url}?page=${nextPageCount}&per-page=${$('#show-record option:selected').val()}">Next</a></li>
                  <li class="page-item ${disabledLastLi}"><a class="page-link" href="${baseUrl}${url}?page=${totalRow}&per-page=${$('#show-record option:selected').val()}">Last</a></li></ul>
          </div>`;
  $('.loadPagination').empty().html(pagination);
}
        </script>
      
    <%- include ('../template/footer') -%>
