<%- include ('../template/header') -%>
    <%- include ('../template/navbar') -%>
        <div class="container-fluid container-fluid-width mt-3">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <h2 class="heading-bold m-0">Profile</h2>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width mt-3 mb-3">
            <div class="row justify-content-center">
                <div class="col-md-6 passport-scan-bg-white br-15 p-4">
                    <form class="form-create-user" id="form-create-user" novalidate>
                        <input type="hidden" id="id" value="<%- userDetails.user_id -%>" />
                        <div class="form-row">
                            <div class="col-md-6 form-group">
                                <label>First name</label>
                                <input type="text" id="first-name" name="first-name" class="form-control no-ob">
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Last name</label>
                                <input type="text" id="last-name" name="last-name" class="form-control no-ob">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="col-md-6 form-group">
                                <label>Email</label>
                                <input type="email" id="email" name="email" class="form-control no-ob" readonly>
                            </div>
                            <div class="col-md-6 form-group">
                                <label>Password <span id="ps-st-q" data-container="body" data-toggle="popover" data-placement="right" data-content="Password must be 8 to 20 characters and it contain uppercase, lowercase, number, special characters"><i class="fa fa-question-circle-o"></i></span></label>
                                <input type="password" id="password" name="password" class="form-control no-ob">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="col-md-6 form-group">
                                <label>Phone</label>
                                <input type="tel" id="phone" name="phone" class="form-control no-ob">
                            </div>
                            <div class="col-md-6 form-group d-none">
                                <label>Select group</label>
                                <select data-placeholder="Choose the group" id="group" name="group"
                                    class="chosen-select form-control no-ob"></select>
                            </div>
                        </div>
                        <div class="form-check form-group mb-5 d-none">
                            <input type="checkbox" class="form-check-input" id="is-active" name="is-active">
                            <label class="form-check-label" for="is-active">Is active?</label>
                        </div>
                        <div class="form-group mt-3">
                            
                            <button class="btn form-submit-btn scan-btn small-btn passport-scan-bg-green passport-scan-text-color-white"
                                type="submit">Update</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <script>
             var oldEmail = '';
             var oldPassword='';
            $(document).ready(function () {
                $("#phone").inputmask({
                    mask: '(+99) ************',
                    placeholder: ' ',
                    showMaskOnHover: false,
                    showMaskOnFocus: false
                });
                $('#password').on('keyup',function(){
                    if($('#id').val() && $('#password').val() !== oldPassword){
                        $( "#password" ).rules( "add", {strong_password:true} );
                    }else{
                        $( "#password" ).rules( "remove", "strong_password" );
                    }
                });
                $('[data-toggle="popover"]').popover();
                $('#email').keyup(function () {
                    if ($(this).val() !== '' && oldEmail !== $(this).val()) {
                        $.ajax({
                            url: `${apiUrl}validate/email`,
                            method: 'POST',
                            data: {
                                email: $('#email').val()
                            }, dataType: 'json',
                            beforeSend: function (xhr) {
                                xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                            }, success: function (res) {
                                validateEmail(res.status);
                            }, error: function (error, execution) {
                                var msg = getErrorMessage(error, execution);
                                cuteAlert({
                                    type: "error",
                                    title: "Validate email",
                                    message: msg,
                                    buttonText: "Ok"
                                  });
                            }, complete: function () {

                            }
                        });
                    } else {
                        validateEmail(false);
                    }
                });
                getByIdDetails($('#id').val());
                $('#form-create-user').validate({
                    rules: {
                        'first-name': {
                            required: true
                        }, 'last-name': {
                            required: true
                        }, 'email': {
                            required: {
                                depends: function () {
                                    $(this).val($.trim($(this).val()));
                                    return true;
                                }
                            },
                            email: true
                        }, 'password': {
                            required: true,
                            strong_password:true
                        }, group: {
                            required: true
                        },phone:{required:true,minlength:18}
                    },
                    highlight: function (input) {
                        $(input).addClass('error-validation');
                    },
                    unhighlight: function (input) {
                        $(input).removeClass('error-validation');
                    },
                    errorPlacement: function (error, element) {
                        $(element).parents('.form-group').append(error);
                    },
                    messages: {
                        'first-name': {
                            required: "Provide the first name"
                        }, 'last-name': {
                            required: 'Provide the last name'
                        }, 'email': {
                            required: 'Provide the email',
                            email: 'Provide valid email'
                        }, 'password': {
                            required: 'Provide the password'
                        }, group: {
                            required: 'Please choose the group'
                        },phone:{required:'Provide the phone number',minlength:"Please enter valid number"}
                    },
                    submitHandler: function () {
                        $.ajax({
                            url: `${apiUrl}profile/${$('#id').val()}`,
                            method: 'PUT',
                            data: {
                                firstName: $('#first-name').val(),
                                lastName: $('#last-name').val(),
                                email: $('#email').val(),
                                password: $('#password').val(),
                                phone: ($('#phone').val()).replace(/[^0-9]/g, "")
                            }, dataType: 'json',
                            beforeSend: function (xhr) {
                                xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                            }, success: function (res) {
                                cuteAlert({
                                    type:res.status?"success": "error",
                                    title: "update profile",
                                    message:res.msg,
                                    buttonText: "Ok"
                                  }).then(()=>{
                                    res.status === true ? window.location.reload(): null;
                                  });
                            }, error: function (error, execution) {
                                var msg = getErrorMessage(error, execution);
                                cuteAlert({
                                    type: "error",
                                    title: "Update profile",
                                    message: msg,
                                    buttonText: "Ok"
                                  })
                            }, complete: function () {

                            }
                        });
                    }
                });
                $("select").chosen().change(function () {
                    $("#form-create-user").validate().element(this);
                });
            });
            function validateEmail(status) {
                if (status) {
                    if ($(document).find('.error-email').length == 0) {
                        $('<label class="error-email">Please email already exist</label>').insertAfter('#email');
                    }
                } else {
                    $('#email').next('label.error-email').remove();
                }
                $('.form-submit-btn').attr('disabled',status);
            }
            function getByIdDetails(id) {
                $.ajax({
                    url: `${apiUrl}user/${id}`,
                    method: 'GET',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: function (res) {
                        if (res.status === true) {
                            $( "#password" ).rules( "remove", "strong_password" );
                            $('#first-name').val(res.data.first_name);
                            $('#last-name').val(res.data.last_name);
                            $('#email').val(res.data.email);
                            $('#password').val(res.data.password);
                            $('#phone').val(res.data.phone);
                            $('#is-active').prop('checked',res.data.is_active === 'Y' ? true:false);
                            $('#group').val(res.data.group_id).trigger("chosen:updated");
                            oldEmail = res.data.email;
                            oldPassword= res.data.password;
                        } else {

                        }
                    }, error: function (error, execution) {
                        var msg = getErrorMessage(error, execution);
                        cuteAlert({
                            type: "error",
                            title: "Get details",
                            message: msg,
                            buttonText: "Ok"
                          })
                    }, complete: function () {

                    }
                });
            }
        </script>
        <%- include ('../template/footer') -%>