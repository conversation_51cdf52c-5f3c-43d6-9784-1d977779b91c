<%- include ('../template/header') -%>
    <%- include ('../template/navbar') -%>
        <div class="container-fluid container-fluid-width mt-3">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <h2 class="heading-bold m-0">
                        <% if(action==='edit' ){ %>
                            Update country
                            <% }else{ %>
                                Create country
                                <% } %>
                    </h2>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width mt-3 mb-3">
            <div class="row justify-content-center">
                <div class="col-md-6 passport-scan-bg-white br-15 responsive-col-12 p-4 pb-32">
                    <form class="form-create-country" id="form-create-country">
                        <input type="hidden" id="id" value="<%- id -%>" />
                        <div class="form-row">
                            <div class="col-6 form-group">
                                <label>Country *</label>
                                <input type="text" id="country" name="country" class="form-control">
                            </div>
                            <div class="col-6 form-group">
                                <label>Country three code *</label>
                                <input type="text" id="short_name" name="short_name" class="form-control">
                            </div>
                            <div class="col-6 form-group">
                                <label>Country two code *</label>
                                <input type="text" id="short_name_two" name="short_name_two" class="form-control">
                            </div>
                            <div class="col-6 form-group">
                                <label>Country number code *</label>
                                <input type="number" min="0" id="country_id" name="country_id" class="form-control" onkeydown="javascript: return ['Backspace','Delete','ArrowLeft','ArrowRight'].includes(event.code) ? true : !isNaN(Number(event.key)) && event.code!=='Space'">
                            </div>
                            
                        </div>
                        <div class="form-row">
                            <div class="form-check form-group">
                                <input type="checkbox" class="form-check-input" id="is-active" name="is-active">
                                <label class="form-check-label" for="is-active">Is active?</label>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="col-6 form-group mt-3">
                                <button
                                    class="btn form-submit-btn small-btn scan-btn passport-scan-bg-green passport-scan-text-color-white"
                                    type="submit">
                                    <% if(action==='edit' ){ %>
                                        Update
                                        <% }else{ %>
                                            Create
                                            <% } %>
                                </button>
                            </div>
                            <div class="form-group col-6 text-right mt-3">
                                <a href="<%- baseUrl -%>country" class="link-green mt-2" type="button">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <script>
            var pagePermission = JSON.parse(`<%- JSON.stringify(permission[0]) -%>`);
            $(document).ready(function () {
                if ($('#id').val() > 0) {
                    getByIdDetails($('#id').val());
                }
                $('#form-create-country').validate({
                    rules: {
                        'short_name': {
                            required: true,
                            minlength:1,
                            maxlength:3
                        },'short_name_two': {
                            required: true,
                            minlength:1,
                            maxlength:2
                        },'country_id': {
                            required: true,
                            number:true
                        },'country': {
                            required: true
                        }
                    },
                    highlight: function (input) {
                        $(input).addClass('error-validation');
                    },
                    unhighlight: function (input) {
                        $(input).removeClass('error-validation');
                    },
                    errorPlacement: function (error, element) {
                        $(element).parents('.form-group').append(error);
                    },
                    messages: {
                        'short_name': {
                            required: 'Please enter the country three code'
                        },'country': {
                            required: 'Please enter the country name'
                        },short_name_two:{
                            required: 'Please enter the country two code'
                        },country_id:{
                            required: 'Please enter the country number code'
                        }
                    },
                    submitHandler: function () {
                        if($('#id').val() > 0){
                            if(pagePermission.edit === 'N'){
                                cuteToast({
                                    type: "warning", 
                                    message: "You have not update permission",
                                    timer: 5000
                                    })
                                return;
                            }
                        }else{
                            if(pagePermission.create === 'N'){
                                cuteToast({
                                    type: "warning", 
                                    message: "You have not create permission",
                                    timer: 5000
                                    })
                                return;
                            }
                        }
                        $.ajax({
                            url: $('#id').val() > 0 ? `${apiUrl}country/${$('#id').val()}` : `${apiUrl}country`,
                            method: $('#id').val() > 0 ? 'PUT' : 'POST',
                            data: {
                                country: $('#country').val(),
                                shortName: $('#short_name').val(),
                                shortNameTwo:$('#short_name_two').val(),
                                countryId:$('#country_id').val(),
                                is_active:$('#is-active').is(':checked')?'A':'D'
                            }, dataType: 'json',
                            beforeSend: function (xhr) {
                                xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                            }, success: function (res) {
                                cuteAlert({
                                    type: res.status ? 'success':'error',
                                    title: "Country",
                                    message: res.msg,
                                    buttonText: "Ok"
                                  }).then(()=>{
                                    res.status === true ? window.location.href = `${baseUrl}country` : null;
                                  });
                              
                            }, error: function (error, execution) {
                                var msg = getErrorMessage(error, execution);
                                cuteAlert({
                                    type: 'error',
                                    title: "Country",
                                    message: msg,
                                    buttonText: "Ok"
                                  });
                            }, complete: function () {

                            }
                        });
                    }
                });
            });
            function getByIdDetails(id) {
                $.ajax({
                    url: `${apiUrl}country/${id}`,
                    method: 'GET',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: function (res) {
                        if (res.status === true) {
                            $('#country').val(res.data.name);
                            $('#short_name').val(res.data.short_name);
                            $('#short_name_two').val(res.data.short_name_two);
                            $('#country_id').val(res.data.country_id);
                            $('#is-active').prop('checked',res.data.is_active ==='A'?true:false);
                        } else {
                            cuteAlert({
                                type: 'error',
                                title: "Country",
                                message: res.msg,
                                buttonText: "Ok"
                              });
                           
                        }
                    }, error: function (error, execution) {
                        var msg = getErrorMessage(error, execution);
                        cuteAlert({
                            type: 'error',
                            title: "Country",
                            message: msg,
                            buttonText: "Ok"
                          })
                    }, complete: function () {

                    }
                });
            }
        </script>

        <%- include ('../template/footer') -%>
