#!/usr/bin/env node

/*
 Author: <PERSON><PERSON><PERSON><PERSON>
 Description: Initial config APP
 */

 var app = require('../app');
 var debug = require('debug')('website:server');
 var http = require('http');
 
 /**
  * Get port from environment and store in Express.
  */
 
 var port = normalizePort(process.env.PORT || '5000');
 app.set('port', port);
 
 /**
  * Create HTTP server.
  */
 
 var server = http.createServer(app);
 
 /**
  * Listen on provided port, on all network interfaces.
  */
 
 server.listen(port);
 server.on('error', onError);
 server.on('listening', onListening);
 
 /**
  * Normalize a port into a number, string, or false.
  */
 
 function normalizePort(val) {
   var port = parseInt(val, 10);
 
   if (isNaN(port)) {
     // named pipe
     return val;
   }
 
   if (port >= 0) {
     // port number
     return port;
   }
 
   return false;
 }
 
 /**
  * Event listener for HTTP server "error" event.
  */
 
 function onError(error) {
   if (error.syscall !== 'listen') {
     throw error;
   }
 
   var bind = typeof port === 'string'
     ? 'Pipe ' + port
     : 'Port ' + port;
 
   // handle specific listen errors with friendly messages
   switch (error.code) {
     case 'EACCES':
       console.error(bind + ' requires elevated privileges');
       process.exit(1);
       break;
     case 'EADDRINUSE':
       console.error(bind + ' is already in use');
       process.exit(1);
       break;
     default:
       throw error;
   }
 }
 
 /**
  * Event listener for HTTP server "listening" event.
  */
 
 function onListening() {
   var addr = server.address();
   var bind = typeof addr === 'string'
     ? 'pipe ' + addr
     : 'port ' + addr.port;
   debug('Listening on ' + bind);
 }
 // above working 





// /**
//  * Module dependencies.
//  */
// var app = require('../app');
// var debug = require('debug')('website:server');
// var http = require('http');
// var { createSymbolicLink } = require('../setupSymbolicLink'); // Require the symbolic link creation function

// /**
//  * Normalize a port into a number, string, or false.
//  */
// function normalizePort(val) {
//   var port = parseInt(val, 10);

//   if (isNaN(port)) {
//     // named pipe
//     return val;
//   }

//   if (port >= 0) {
//     // port number
//     return port;
//   }

//   return false;
// }

// /**
//  * Event listener for HTTP server "error" event.
//  */
// function onError(error) {
//   if (error.syscall !== 'listen') {
//     throw error;
//   }

//   var bind = typeof port === 'string'
//     ? 'Pipe ' + port
//     : 'Port ' + port;

//   // handle specific listen errors with friendly messages
//   switch (error.code) {
//     case 'EACCES':
//       console.error(bind + ' requires elevated privileges');
//       process.exit(1);
//       break;
//     case 'EADDRINUSE':
//       console.error(bind + ' is already in use');
//       process.exit(1);
//       break;
//     default:
//       throw error;
//   }
// }

// /**
//  * Event listener for HTTP server "listening" event.
//  */
// function onListening() {
//   var addr = server.address();
//   var bind = typeof addr === 'string'
//     ? 'pipe ' + addr
//     : 'port ' + addr.port;
//   debug('Listening on ' + bind);
// }

// /**
//  * Get port from environment and store in Express.
//  */
// var port = normalizePort(process.env.PORT || '5000');
// app.set('port', port);

// /**
//  * Create HTTP server.
//  */
// var server = http.createServer(app);

// /**
//  * Create symbolic link and start HTTP server.
//  */
// async function initializeApp() {
//   try {
//     // Create the symbolic link
//     await createSymbolicLink();
//     console.log('Symbolic link created successfully.');

//     // Listen on provided port, on all network interfaces
//     server.listen(port);
//     server.on('error', onError);
//     server.on('listening', onListening);
//   } catch (error) {
//     console.error('Failed to create symbolic link:', error);
//     process.exit(1); // Terminate the process with a non-zero exit code
//   }
// }

// // Initialize the application (create symbolic link and start HTTP server)
// initializeApp();

 
 
 
 // var app = require('../app');
 // var debug = require('debug')('website:server');
 // var http = require('http');
 // const cors = require('cors');
 // /**
 //  * Get port from environment and store in Express.
 //  */
 
 // var port = normalizePort(process.env.PORT || '3000');
 // app.set('port', port);
 
 // /**
 //  * Create HTTP server.
 //  */
 
 // var server = http.createServer(app);
 
 // /**
 //  * Listen on provided port, on all network interfaces.
 //  */
 
 // server.listen(port);
 // server.on('error', onError);
 // server.on('listening', onListening);
 
 // /**
 //  * Normalize a port into a number, string, or false.
 //  */
 
 // function normalizePort(val) {
 //   var port = parseInt(val, 10);
 
 //   if (isNaN(port)) {
 //     // named pipe
 //     return val;
 //   }
 
 //   if (port >= 0) {
 //     // port number
 //     return port;
 //   }
 
 //   return false;
 // }
 
 // /**
 //  * Event listener for HTTP server "error" event.
 //  */
 
 // function onError(error) {
 //   if (error.syscall !== 'listen') {
 //     throw error;
 //   }
 
 //   var bind = typeof port === 'string'
 //     ? 'Pipe ' + port
 //     : 'Port ' + port;
 
 //   // handle specific listen errors with friendly messages
 //   switch (error.code) {
 //     case 'EACCES':
 //       console.error(bind + ' requires elevated privileges');
 //       process.exit(1);
 //       break;
 //     case 'EADDRINUSE':
 //       console.error(bind + ' is already in use');
 //       process.exit(1);
 //       break;
 //     default:
 //       throw error;
 //   }
 // }
 
 // /**
 //  * Event listener for HTTP server "listening" event.
 //  */
 
 // function onListening() {
 //   var addr = server.address();
 //   var bind = typeof addr === 'string'
 //     ? 'pipe ' + addr
 //     : 'port ' + addr.port;
 //   debug('Listening on ' + bind);
 //   debug('Base URL: ' + getBaseUrl(addr)); // Log the dynamically determined base URL
 // }
 
 // /**
 //  * Get the base URL based on the incoming request address.
 //  */
 
 // function getBaseUrl(addr) {
 //   var ip = addr.address === '::' ? 'localhost' : addr.address;
 //   var baseUrl = 'http://' + ip + ':' + addr.port;
 //   return baseUrl;
 // }
 // // Allow requests from any origin
 // app.use(cors());