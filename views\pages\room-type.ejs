<%- include ('../template/header') -%>
    <%- include ('../template/navbar') -%>
        <div class="container-fluid container-fluid-width mt-3">
            <div class="row">
                <div class="col-6">
                    <h2 class="heading-bold m-0">Room type</h2>
                </div>
                <div class="col-6">
                    <% if(permission[0].create==='Y' ){ %>
                        <div class="form-group m-0 text-right">
                            <a href="<%- baseUrl -%>room-type/create -%>"
                                class="btn small-btn  mr-3 scan-btn passport-scan-bg-green passport-scan-text-color-white"><i
                                class="fa fa-plus"></i> create</a>
                        </div>
                        <% } %>
                </div>
            </div>
        </div>
      
        <div class="container-fluid container-fluid-width mt-3 passport-scan-bg-white br-15 p-4 pb-4">
                    <div class="row">
                        <div class="col-md-12 d-flex justify-content-end mb-2">
                            <div class="form-group mr-2">
                                <select id="show-record" class="form-control no-ob">
                                    <option value="10">10</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="search" id="search" class="form-control no-ob" placeholder="Search">
                            </div>
                            <div class="form-group ml-3">
                                <button type="button"
                                    class="btn scan-btn small-btn btn-search passport-scan-bg-green passport-scan-text-color-white">Search</button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th data-field="s_no" data-sortable="false">#</th>
                                            <th data-field="room_type" data-sortable="true">Room Type</th>
                                            <th data-field="is_active" data-sortable="false">Status</th>
                                            <th data-field="action" data-sortable="false">Action</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            
        <div class="container-fluid container-fluid-width">
            <div class="row">
                <div class="col-md-12">
                    <div class="loadPagination row  mt-4 mb-5"></div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            var pagePermission = JSON.parse(`<%- JSON.stringify(permission[0]) -%>`);
            var url = new URL(window.location.href);
            var searchParams = new URLSearchParams(url.search);
            $(document).ready(function () {
                searchParams.get('per-page') ? $('#show-record').val(searchParams.get('per-page')) : '';
                getList();
                $('.btn-search').click(function () {
                    getList();
                });
                $('#show-record').change(function () {
                    window.location.href = `${baseUrl}room-type?per-page=${$('option:selected', this).val()}`;
                    getList();
                });
            });
            function getList() {
                var page = searchParams.get('page');
                var pageId = page ? `&page=${page}` : `&page=1`;
                var beforeQ = page ? '&' : '&';
                var search = $('#search').val() !== "" ? (beforeQ + encodeURI(`search=${$('#search').val()}`)) : '';

                $.ajax({
                    url: `${apiUrl}room-type?total-page=${$('#show-record option:selected').val()}${pageId}${search}`,
                    method: 'get',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    },
                    success: function (res) {
                        if (res.status === true) {
                            var myData = [];
                            $.each(res.data, function (ind, row) {
                                var _editBtn = pagePermission.edit === 'Y' ? `<a class="mr-2" href="<%- baseUrl -%>room-type/edit/${row.room_type_id}"><i class="fa fa-pencil"></i></a>` : '';
                                var _deleteBtn = pagePermission.edit === 'Y' ? `<a class="text-danger" href="javascript:;" onclick="deleteroomType(${row.room_type_id})"><i class="fa fa-trash"></i></a>` : '';
                                myData.push({
                                    "s_no": (parseInt(res.offset) + parseInt(ind + 1)),
                                    "room_type": row.name,
                                    "is_active":row.is_active ==='A'?'<span class="badge badge-success text-small">Active</span>' : '<span class="badge badge-secondary text-small">Deactive</span>',
                                    "action": `${_editBtn} ${_deleteBtn}`
                                });

                            });
                            $('table').bootstrapTable('destroy');
                            $('table').bootstrapTable({
                                data: myData
                            });
                            bindPagination(page, res.totalPages, 'room-type');
                        }
                    }
                });
            }
            function deleteroomType(id) {
                cuteAlert({
                    type: "question",
                    title: "Delete the Room Type",
                    message: "Are you sure do you want to delete?",
                    confirmText: "Yes",
                    cancelText: "No"
                }).then((e) => {
                    if (e == 'confirm') {
                        $.ajax({
                            url: `${apiUrl}room-type/${id}`,
                            type: 'delete',
                            dataType: 'json',
                            headers: {
                                "Authorization": "Bearer <%- userDetails.token -%>"
                            }, success: function (res) {
                                cuteAlert({
                                    type: res.status ? "success" : "error",
                                    title: "Delete Room Type",
                                    message: res.msg,
                                    buttonText: "Ok"
                                }).then(() => {
                                    res.status ? getList() : null;
                                });
                            }, complete: function () {

                            }
                        })
                    }
                })


            }

            
            function bindPagination(page, totalRow, url) {
  var firstBtnState = page <= 1 ? 'disabled' : '';
  var previousBtnState = page <= 1 ? 'javascript:;' : baseUrl + `${url}?page=` + (parseInt(page) - 1) + `&per-page=${$('#show-record option:selected').val()}`;
  var disabledFirstLi = page == 1 ? 'disabled' : '';
  var pagination = `<div class="col-12">
                  <ul class="pagination justify-content-end">
                      <li class="page-item ${disabledFirstLi}"><a href="${baseUrl}${url}?page=1&per-page=${$('#show-record option:selected').val()}" class="page-link">First</a></li>
                      <li class="${firstBtnState} page-item">
                          <a href="${previousBtnState}" class="page-link">Prev</a>
                      </li>`;

  var disabledLi = 1 <= totalRow ? 'disabled' : '';
  var nextPageCount = page >= totalRow ? parseInt(page) : (parseInt(page ? page : 1) + 1);
  var disabledLastLi = page == totalRow ? 'disabled' : '';

  pagination += ` <li class="${disabledLi} page-item"><a class="page-link" href="${baseUrl}${url}?page=${nextPageCount}&per-page=${$('#show-record option:selected').val()}">Next</a></li>
                  <li class="page-item ${disabledLastLi}"><a class="page-link" href="${baseUrl}${url}?page=${totalRow}&per-page=${$('#show-record option:selected').val()}">Last</a></li></ul>
          </div>`;
  $('.loadPagination').empty().html(pagination);
}
        </script>
        <%- include ('../template/footer') -%>
