<% /* %>
Description: Dashboard page for admin and user
<% */ %>
<%- include ('../template/header') -%>
    <%- include ('../template/navbar') -%>
        <div class="container-fluid container-fluid-width mt-3">
            <div class="row">
                <div class="col-md-9">
                    <h2 class="heading-bold m-0">Dashboard</h2>
                </div>
                <div class="col-md-3 responsive-mt-15<% if(userDetails.user_type==='user') { %> d-none <% } %>">
                    <select id="property" class="form-control"></select>
                </div>
            </div>
        </div>
        <div class="container-fluid container-fluid-width mt-3 mb-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="box-square">
                        <div class="d-flex justify-content-around">
                            <div class="dashboard-icon align-self-center purple-bg"><i class="fa fa-building"
                                    aria-hidden="true"></i></div>
                            <div class="align-self-center">
                                <p class="m-0 font-size-16">Rooms available</p>
                            </div>
                            <div class="align-self-center">
                                <h6 class="m-0" id="room-available-count">0</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 responsive-mt-25">
                    <div class="box-square">
                        <div class="d-flex justify-content-around">
                            <div class="dashboard-icon align-self-center orange-bg"><i class="fa fa-bed"
                                    aria-hidden="true"></i></div>
                            <div class="align-self-center">
                                <p class="m-0 font-size-16">Rooms Occupied</p>
                            </div>
                            <div class="align-self-center">
                                <h6 class="m-0" id="room-occupied-count">0</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 responsive-mt-25">
                    <div class="box-square">
                        <div class="d-flex justify-content-around">
                            <div class="dashboard-icon align-self-center pink-bg"><i class="fa fa-folder-open"
                                    aria-hidden="true"></i></div>
                            <div class="align-self-center">
                                <p class="m-0 font-size-16">In tray</p>
                            </div>
                            <div class="align-self-center">
                                <h6 class="m-0" id="in-tray-count">0</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 responsive-mt-25">
                    <div class="box-square">
                        <div class="d-flex justify-content-around">
                            <div class="dashboard-icon align-self-center sky-bg"><i class="fa fa-tasks"
                                    aria-hidden="true"></i></div>
                            <div class="align-self-center">
                                <p class="m-0 font-size-16">Processing tray</p>
                            </div>
                            <div class="align-self-center">
                                <h6 class="m-0" id="processing-count">0</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 chart-top-m">
                    <div class="box-square chart-height">
                        <div class="d-flex justify-content-between mt-2">
                            <div class="">
                                <label>From Date</label>
                                <input type="text" id="from-date" class="cus-form-control mb-2" size="10">
                                <label>To Date</label>
                                <input type="text" id="to-date" class="cus-form-control mb-2" size="10">
                                <button type="button"
                                    class="btn scan-btn btn-search passport-scan-bg-green passport-scan-text-color-white">Search</button>
                            </div>
                            <div class="">
                                <div class="row justify-content-end">
                                    <div class="dropdown">
                                        <button class="dropdown-toggle border-hide-drop-down-btn" type="button"
                                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fa fa-bars" aria-hidden="true"></i>
                                        </button>
                                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                            <a class="dropdown-item" href="javascript:;"
                                                onclick="getDashboardInHouseReportExcel()">Export excel</a>
                                            <a class="dropdown-item" href="javascript:;"
                                                onclick="getDashboardInHouseReportPdf()">Export pdf</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <canvas id="pieChart" width="300" height="300"></canvas>
                        </div>
                    </div>
                </div>
           
                <div class="col-md-6 chart-top-m">
                    <div class="box-square chart-height">
                        <div class="row justify-content-end mt-2">
                            <div class="dropdown">
                                <button class="dropdown-toggle border-hide-drop-down-btn" type="button"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-bars" aria-hidden="true"></i>
                                </button>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                    <a class="dropdown-item" href="javascript:;"
                                        onclick="getWeeklyCheckInReportExcel()">Export excel</a>
                                    <a class="dropdown-item" href="javascript:;"
                                        onclick="getWeeklyCheckInReportPdf()">Export pdf</a>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <canvas id="lineChart"></canvas>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <script>
            var lastWeekDate = getLastWeekDates();
            var dataValue, myPieChart, myLineChart;
            var currentDate = moment().format('DD-MM-YYYY');
            var _ut = '<%- userDetails.user_type -%>';
            $(document).ready(function () {
                $('#from-date').val(currentDate);
                $('#to-date').val(currentDate);
                $('#from-date,#to-date').datetimepicker({
                    format: 'd-m-Y',
                    timepicker: false
                });
                if(_ut ==='admin'){
                    $('#property').chosen();
                    getPropertyDetails();
                }else{
                    callDetails();
                }
                $('#property').change(function(){
                    callDetails();
                });

                $('.btn-search').click(function () {
                    getInHouseChart($('#from-date').val(), $('#to-date').val());
                });
                
                $('.fa-folder-open').click(function () {
                    window.location.href = `${baseUrl}document-details`;
                });
                $('.fa-tasks').click(function () {
                    window.location.href = `${baseUrl}document-details?processing`;
                });

                
            });
            function callDetails(){
                getTheCounts();
                getInHouseChart(currentDate, currentDate);
                getWeeklyChart(lastWeekDate.fromDate, lastWeekDate.endDate);
            }
            function getPropertyDetails() {
                $.ajax({
                    url: `${apiUrl}dashboard/get-property-list`,
                    method: 'GET',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: function (res) {
                        if (res.status === true) {
                            var propertyOption = '';
                            $.each(res.data,function(ind,row){
                                propertyOption += `<option value="${row.property_id}" ${ind===0 ? 'selected':''}>${row.name}</option>`;
                            });
                           $('#property').empty().html(propertyOption).trigger('chosen:updated');
                           callDetails();
                        }
                    }, error: function (error, execution) {
                        var msg = getErrorMessage(error, execution);
                        cuteAlert({
                            type:"error",
                            title: "Get chart details",
                            message:msg,
                            buttonText: "Ok"
                          });
                    }, complete: function () {
                    }
                });
            }
            function getSelectOptionsList() {
                $.ajax({
                    url: `${apiUrl}dashboard/get-counts`,
                    method: 'GET',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: function (res) {
                        if (res.status === true) {
                            $('#room-available-count').text(res.data.available);
                            $('#room-occupied-count').text(res.data.occupied);
                            $('#in-tray-count').text(res.data.inTray);
                            $('#processing-count').text(res.data.processing);
                        }
                    }, error: function (error, execution) {
                        var msg = getErrorMessage(error, execution);
                        cuteAlert({
                            type: "error",
                            title: "Get chart details",
                            message:msg,
                            buttonText: "Ok"
                          });
                    }, complete: function () {
                    }
                });
            }
            function getTheCounts() {
                $.ajax({
                    url: `${apiUrl}dashboard/get-counts${_ut === 'admin' ? '?property_id='+$('#property option:selected').val():''}`,
                    method: 'GET',
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: function (res) {
                        if (res.status === true) {
                            $('#room-available-count').text(res.data.available);
                            $('#room-occupied-count').text(res.data.occupied);
                            $('#in-tray-count').text(res.data.inTray);
                            $('#processing-count').text(res.data.processing);
                        }
                    }, error: function (error, execution) {
                        var msg = getErrorMessage(error, execution);
                        cuteAlert({
                            type: "error",
                            title: "Get chart details",
                            message:msg,
                            buttonText: "Ok"
                          });
                    }, complete: function () {
                    }
                });
            }
            function getInHouseChart(fromDate, endDate) {
                $.ajax({
                    url: `${apiUrl}dashboard/get-inhouse-data${_ut === 'admin' ? '?property_id='+$('#property option:selected').val():''}`,
                    method: 'POST',
                    data: {
                        fromDate: fromDate,
                        endDate: endDate
                    },
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: async function (res) {
                        var pieLable = [];
                        var pieData = [];
                        var pieBackground = [];
                        if (res.status === true) {
                            await $.each(res.data, async function (j, k) {
                                pieLable.push(k.name);
                                pieData.push(k.data_count);
                                pieBackground.push(dynamicColors());
                            });

                        } else {
                            pieLable.push('No data found');

                            pieData.push(100);

                            pieBackground.push(dynamicColors());
                        }


                        var ctx = document.getElementById('pieChart').getContext('2d');

                        if (myPieChart) {
                            myPieChart.destroy();
                        }
                        var chartData = await {
                            labels: pieLable,
                            datasets: [
                                {
                                    data: pieData,
                                    backgroundColor: pieBackground
                                }
                            ]
                        };
                        myPieChart = new Chart(ctx, {
                            type: 'pie',
                            data: chartData,
                            options: {
                                responsive: false,
                                plugins: {
                                    legend: {
                                        position: 'top',
                                    },
                                    title: {
                                        display: true,
                                        text: 'In-House by Nationality'
                                    }
                                }
                            },
                        });

                    }, error: function (error, execution) {
                        var msg = getErrorMessage(error, execution);
                       
                        cuteAlert({
                            type: "error",
                            title: "Get in-inhouse by nationality",
                            message:msg,
                            buttonText: "Ok"
                          });
                    }, complete: function () {
                    }
                });
            }
            function getWeeklyChart(fromDate, endDate) {
                $.ajax({
                    url: `${apiUrl}dashboard/get-weekly-data${_ut === 'admin' ? '?property_id='+$('#property option:selected').val():''}`,
		    method: 'POST',
                    data: {
                       fromDate: fromDate,
                      endDate: endDate
                    },
	
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <%- userDetails.token -%>');
                    }, success: async function (res) {
                        var DATA_COUNT = (res.dateList).length;
                        var labels = [];
                        var lineData = [];
                        if (res.status === true) {
                            for (var i = 0; i < DATA_COUNT; ++i) {
                                labels.push(moment(res.dateList[i], "YYYY-MM-DD").format('DD-MM-YYYY'));

                                var re = _.find(res.data, function (o) {
                                    return o.check_in_date === res.dateList[i];
                                });
                                var range = typeof re === 'undefined' ? 0 : re.data_count;
                                lineData.push(range);
                            }
                        } else {
                            for (var i = 0; i < DATA_COUNT; ++i) {
                                labels.push(moment(res.dateList[i], "YYYY-MM-DD").format('DD-MM-YYYY'));
                            }
                        }

                        var ctx = document.getElementById('lineChart').getContext('2d');
                        var chartData = await {
                            labels: labels,
                            datasets: [
                                {
                                    label: 'Chcek in',
                                    data: lineData,
                                    borderColor: 'rgba(24, 152, 92, 1)',
                                    fill: true
                                }
                            ]
                        };
                        var myChart = new Chart(ctx, {
                            type: 'line',
                            data: chartData,
                            options: {
                                responsive: true,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Weekly check-in'
                                    },
                                },
                                interaction: {
                                    intersect: false,
                                },
                                scales: {
                                    x: {
                                        display: true,
                                        title: {
                                            display: true,
                                            text: ''
                                        },
                                        ticks: {
                                            autoSkip: false,
                                            // maxRotation: 45,
                                            minRotation: 45
                                        }
                                    },
                                    y: {
                                        display: true,
                                        title: {
                                            display: true,
                                            text: 'No.of Checkin'
                                        },
                                        // suggestedMin: -10,
                                        // suggestedMax: 200
                                    }
                                }
                            },
                        });
                    }, error: function (error, execution) {
			 console.log(JSON.stringify(error));
                        var msg = getErrorMessage(error, execution);
                        cuteAlert({
                            type:"error",
                            title: "Get weekly check-in",
                            message:msg,
                            buttonText: "Ok"
                          });
                    }, complete: function () {
                    }
                });
            }

            function bindLineChart() {
                var DATA_COUNT = 12;
                var labels = [];
                for (var i = 0; i < DATA_COUNT; ++i) {
                    labels.push(i.toString());
                }
                var datapoints = [0, 20, 20, 60, 60, 120, NaN, 180, 120, 125, 105, 110, 170];

                var ctx = document.getElementById('lineChart').getContext('2d');
                var myChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: 'Cubic interpolation (monotone)',
                                data: [0, 20, 20, 60, 60, 120],
                                borderColor: 'rgba(255, 99, 132, 1)',
                                fill: true,
                                cubicInterpolationMode: 'monotone',
                                tension: 0.4
                            }, {
                                label: 'Cubic interpolation',
                                data: [180, 120, 125, 105, 110, 170],
                                borderColor: 'rgba(153, 102, 255, 1)',
                                fill: true,
                                tension: 0.4
                            }, {
                                label: 'Linear interpolation (default)',
                                data: [51, 53, 55, 57, 66],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Weekly check-in'
                            },
                        },
                        interaction: {
                            intersect: false,
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'value'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Value'
                                },
                                suggestedMin: -10,
                                suggestedMax: 200
                            }
                        }
                    },
                });
            }
            function getWeeklyCheckInReportExcel() {
                var from = lastWeekDate.fromDate;
                var to =  lastWeekDate.endDate;
                var userToken = '<%- userDetails.token -%>'
                window.location.href = `${apiUrl}export/dashboard/excel/weekly-check-in?token=${userToken}&fromDate=${from}&endDate=${to}${_ut === 'admin' ? '&property_id='+$('#property option:selected').val():''}`;
            }
            function getWeeklyCheckInReportPdf() {
                var from = lastWeekDate.fromDate;
                var to =  lastWeekDate.endDate;
                var userToken = '<%- userDetails.token -%>'
                window.location.href = `${apiUrl}export/dashboard/pdf/weekly-check-in?token=${userToken}&fromDate=${from}&endDate=${to}${_ut === 'admin' ? '&property_id='+$('#property option:selected').val():''}`;
            }
            function getDashboardInHouseReportExcel() {
                var userToken = '<%- userDetails.token -%>'
                window.location.href = `${apiUrl}export/dashboard/excel/in-house?token=${userToken}&fromDate=${$('#from-date').val()}&endDate=${$('#to-date').val()}${_ut === 'admin' ? '&property_id='+$('#property option:selected').val():''}`;
            }
            function getDashboardInHouseReportPdf() {
                var userToken = '<%- userDetails.token -%>'
                window.location.href = `${apiUrl}export/dashboard/pdf/in-house?token=${userToken}&fromDate=${$('#from-date').val()}&endDate=${$('#to-date').val()}${_ut === 'admin' ? '&property_id='+$('#property option:selected').val():''}`;
            }
        </script>
        <%- include ('../template/footer') -%>
