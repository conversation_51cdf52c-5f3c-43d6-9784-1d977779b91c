module.exports ={
    "country": [
        {
            "short_name": "AFG",
            "short_name_two": "AF",
            "name": "AFGHANISTAN",
            "country_id": "0"
        },
        {
            "short_name": "ALA",
            "short_name_two": "AL",
            "name": "ALAND ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "ALB",
            "short_name_two": "AL",
            "name": "ALBANIA",
            "country_id": "0"
        },
        {
            "short_name": "DZA",
            "short_name_two": "DZ",
            "name": "ALGERIA",
            "country_id": "0"
        },
        {
            "short_name": "ASM",
            "short_name_two": "AS",
            "name": "AMERICAN SAMOA",
            "country_id": "0"
        },
        {
            "short_name": "AND",
            "short_name_two": "AN",
            "name": "ANDORRA",
            "country_id": "0"
        },
        {
            "short_name": "AGO",
            "short_name_two": "AG",
            "name": "ANGOLA",
            "country_id": "0"
        },
        {
            "short_name": "AIA",
            "short_name_two": "AI",
            "name": "ANGUILLA",
            "country_id": "0"
        },
        {
            "short_name": "ATA",
            "short_name_two": "AT",
            "name": "ANTARCTICA",
            "country_id": "0"
        },
        {
            "short_name": "ATG",
            "short_name_two": "AT",
            "name": "ANTIGUA AND BARBUDA",
            "country_id": "0"
        },
        {
            "short_name": "ARG",
            "short_name_two": "AR",
            "name": "ARGENTINA",
            "country_id": "0"
        },
        {
            "short_name": "ARM",
            "short_name_two": "AR",
            "name": "ARMENIA",
            "country_id": "0"
        },
        {
            "short_name": "ABW",
            "short_name_two": "AB",
            "name": "ARUBA",
            "country_id": "0"
        },
        {
            "short_name": "AUS",
            "short_name_two": "AU",
            "name": "AUSTRALIA",
            "country_id": "0"
        },
        {
            "short_name": "AUT",
            "short_name_two": "AU",
            "name": "AUSTRIA",
            "country_id": "0"
        },
        {
            "short_name": "AZE",
            "short_name_two": "AZ",
            "name": "AZERBAIJAN",
            "country_id": "0"
        },
        {
            "short_name": "BHS",
            "short_name_two": "BH",
            "name": "BAHAMAS",
            "country_id": "0"
        },
        {
            "short_name": "BHR",
            "short_name_two": "BH",
            "name": "BAHRAIN",
            "country_id": "0"
        },
        {
            "short_name": "BGD",
            "short_name_two": "BG",
            "name": "BANGLADESH",
            "country_id": "0"
        },
        {
            "short_name": "BRB",
            "short_name_two": "BR",
            "name": "BARBADOS",
            "country_id": "0"
        },
        {
            "short_name": "BLR",
            "short_name_two": "BL",
            "name": "BELARUS",
            "country_id": "0"
        },
        {
            "short_name": "BEL",
            "short_name_two": "BE",
            "name": "BELGIUM",
            "country_id": "0"
        },
        {
            "short_name": "BLZ",
            "short_name_two": "BL",
            "name": "BELIZE",
            "country_id": "0"
        },
        {
            "short_name": "BEN",
            "short_name_two": "BE",
            "name": "BENIN (DAHOMEY)",
            "country_id": "0"
        },
        {
            "short_name": "BMU",
            "short_name_two": "BM",
            "name": "BERMUDA",
            "country_id": "0"
        },
        {
            "short_name": "BTN",
            "short_name_two": "BT",
            "name": "BHUTAN",
            "country_id": "0"
        },
        {
            "short_name": "BOL",
            "short_name_two": "BO",
            "name": "BOLIVIA",
            "country_id": "0"
        },
        {
            "short_name": "BES",
            "short_name_two": "BE",
            "name": "BONAIRE, SINT EUSTATIUS AND SABA",
            "country_id": "0"
        },
        {
            "short_name": "BIH",
            "short_name_two": "BI",
            "name": "BOSNIA AND HERZEGOVINA",
            "country_id": "0"
        },
        {
            "short_name": "BWA",
            "short_name_two": "BW",
            "name": "BOTSWANA",
            "country_id": "0"
        },
        {
            "short_name": "BVT",
            "short_name_two": "BV",
            "name": "BOUVET ISLAND",
            "country_id": "0"
        },
        {
            "short_name": "BRA",
            "short_name_two": "BR",
            "name": "BRAZIL",
            "country_id": "0"
        },
        {
            "short_name": "IOT",
            "short_name_two": "IO",
            "name": "BRITISH INDIAN OCEAN TERRITORY",
            "country_id": "0"
        },
        {
            "short_name": "BRN",
            "short_name_two": "BR",
            "name": "BRUNEI DARUSSALAM",
            "country_id": "0"
        },
        {
            "short_name": "BGR",
            "short_name_two": "BG",
            "name": "BULGARIA",
            "country_id": "0"
        },
        {
            "short_name": "BFA",
            "short_name_two": "BF",
            "name": "BURKINA FASO ( UPPER VOLTA)",
            "country_id": "0"
        },
        {
            "short_name": "BDI",
            "short_name_two": "BD",
            "name": "BURUNDI",
            "country_id": "0"
        },
        {
            "short_name": "KHM",
            "short_name_two": "KH",
            "name": "CAMBODIA (KAMPUCHEA)",
            "country_id": "0"
        },
        {
            "short_name": "CMR",
            "short_name_two": "CM",
            "name": "CAMEROON",
            "country_id": "0"
        },
        {
            "short_name": "CAN",
            "short_name_two": "CA",
            "name": "CANADA",
            "country_id": "0"
        },
        {
            "short_name": "CPV",
            "short_name_two": "CP",
            "name": "CAPE VERDE ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "CYM",
            "short_name_two": "CY",
            "name": "CAYMAN ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "CAF",
            "short_name_two": "CA",
            "name": "CENTRAL AFRICAN REPUBLIC",
            "country_id": "0"
        },
        {
            "short_name": "TCD",
            "short_name_two": "TC",
            "name": "CHAD",
            "country_id": "0"
        },
        {
            "short_name": "CHL",
            "short_name_two": "CH",
            "name": "CHILE",
            "country_id": "0"
        },
        {
            "short_name": "CHN",
            "short_name_two": "CH",
            "name": "CHINA",
            "country_id": "0"
        },
        {
            "short_name": "CXR",
            "short_name_two": "CX",
            "name": "CHRISTMAS ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "CCK",
            "short_name_two": "CC",
            "name": "COCOS (KEELING) ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "COL",
            "short_name_two": "CO",
            "name": "COLOMBIA",
            "country_id": "0"
        },
        {
            "short_name": "COM",
            "short_name_two": "CO",
            "name": "COMOROS",
            "country_id": "0"
        },
        {
            "short_name": "COG",
            "short_name_two": "CO",
            "name": "CONGO",
            "country_id": "0"
        },
        {
            "short_name": "COD",
            "short_name_two": "CO",
            "name": "CONGO, DEMOCRATIC REPUBLIC OF THE (ZAIRE)",
            "country_id": "0"
        },
        {
            "short_name": "COK",
            "short_name_two": "CO",
            "name": "COOK ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "CRI",
            "short_name_two": "CR",
            "name": "COSTA RICA",
            "country_id": "0"
        },
        {
            "short_name": "CIV",
            "short_name_two": "CI",
            "name": "COTE D'IVOIRE",
            "country_id": "0"
        },
        {
            "short_name": "HRV",
            "short_name_two": "HR",
            "name": "CROTIA -REPUBLIC OF CROTIA",
            "country_id": "0"
        },
        {
            "short_name": "CUB",
            "short_name_two": "CU",
            "name": "CUBA",
            "country_id": "0"
        },
        {
            "short_name": "CUW",
            "short_name_two": "CU",
            "name": "CURACAO",
            "country_id": "0"
        },
        {
            "short_name": "CYP",
            "short_name_two": "CY",
            "name": "CYPRUS",
            "country_id": "0"
        },
        {
            "short_name": "CZE",
            "short_name_two": "CZ",
            "name": "CZECH REPUBLIC",
            "country_id": "0"
        },
        {
            "short_name": "DNK",
            "short_name_two": "DN",
            "name": "DENMARK",
            "country_id": "0"
        },
        {
            "short_name": "DJI",
            "short_name_two": "DJ",
            "name": "DJIBOUTI",
            "country_id": "0"
        },
        {
            "short_name": "DMA",
            "short_name_two": "DM",
            "name": "DOMINICA",
            "country_id": "0"
        },
        {
            "short_name": "DOM",
            "short_name_two": "DO",
            "name": "DOMINICAN REPUBLIC",
            "country_id": "0"
        },
        {
            "short_name": "TLS",
            "short_name_two": "TL",
            "name": "EAST TIMOR, DEMOCRATIC REPUBLIC OF",
            "country_id": "0"
        },
        {
            "short_name": "ECU",
            "short_name_two": "EC",
            "name": "ECUADOR",
            "country_id": "0"
        },
        {
            "short_name": "EGY",
            "short_name_two": "EG",
            "name": "EGYPT",
            "country_id": "0"
        },
        {
            "short_name": "SLV",
            "short_name_two": "SL",
            "name": "EL SALVADOR",
            "country_id": "0"
        },
        {
            "short_name": "GNQ",
            "short_name_two": "GN",
            "name": "EQUITORIAL GUINEA",
            "country_id": "0"
        },
        {
            "short_name": "ERI",
            "short_name_two": "ER",
            "name": "ERITREA",
            "country_id": "0"
        },
        {
            "short_name": "EST",
            "short_name_two": "ES",
            "name": "ESTONIA",
            "country_id": "0"
        },
        {
            "short_name": "ETH",
            "short_name_two": "ET",
            "name": "ETHIOPIA",
            "country_id": "0"
        },
        {
            "short_name": "FLK",
            "short_name_two": "FL",
            "name": "FALKLAND ISLANDS (MALVINAS)",
            "country_id": "0"
        },
        {
            "short_name": "FRO",
            "short_name_two": "FR",
            "name": "FAROE ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "FIJ",
            "short_name_two": "FI",
            "name": "FIJI",
            "country_id": "0"
        },
        {
            "short_name": "FIN",
            "short_name_two": "FI",
            "name": "FINLAND",
            "country_id": "0"
        },
        {
            "short_name": "FRA",
            "short_name_two": "FR",
            "name": "FRANCE",
            "country_id": "0"
        },
        {
            "short_name": "FXX",
            "short_name_two": "FX",
            "name": "FRANCE METROPOLITAN",
            "country_id": "0"
        },
        {
            "short_name": "GUF",
            "short_name_two": "GU",
            "name": "FRENCH GUIANA",
            "country_id": "0"
        },
        {
            "short_name": "ATF",
            "short_name_two": "AT",
            "name": "FRENCH SOUTHERN TERRITORIES",
            "country_id": "0"
        },
        {
            "short_name": "GAB",
            "short_name_two": "GA",
            "name": "GABON",
            "country_id": "0"
        },
        {
            "short_name": "GMB",
            "short_name_two": "GM",
            "name": "GAMBIA",
            "country_id": "0"
        },
        {
            "short_name": "GEO",
            "short_name_two": "GE",
            "name": "GEORGIA",
            "country_id": "0"
        },
        {
            "short_name": "DEU",
            "short_name_two": "DE",
            "name": "GERMANY",
            "country_id": "0"
        },
        {
            "short_name": "GHA",
            "short_name_two": "GH",
            "name": "GHANA",
            "country_id": "0"
        },
        {
            "short_name": "GIB",
            "short_name_two": "GI",
            "name": "GIBRALTAR",
            "country_id": "0"
        },
        {
            "short_name": "GRC",
            "short_name_two": "GR",
            "name": "GREECE",
            "country_id": "0"
        },
        {
            "short_name": "GRL",
            "short_name_two": "GR",
            "name": "GREENLAND",
            "country_id": "0"
        },
        {
            "short_name": "GRD",
            "short_name_two": "GR",
            "name": "GRENADA",
            "country_id": "0"
        },
        {
            "short_name": "GLP",
            "short_name_two": "GL",
            "name": "GUADELOUPE",
            "country_id": "0"
        },
        {
            "short_name": "GUM",
            "short_name_two": "GU",
            "name": "GUAM",
            "country_id": "0"
        },
        {
            "short_name": "GTM",
            "short_name_two": "GT",
            "name": "GUATEMALA",
            "country_id": "0"
        },
        {
            "short_name": "GGY",
            "short_name_two": "GG",
            "name": "GUERNSEY",
            "country_id": "0"
        },
        {
            "short_name": "GIN",
            "short_name_two": "GI",
            "name": "GUINEA",
            "country_id": "0"
        },
        {
            "short_name": "GNB",
            "short_name_two": "GN",
            "name": "GUINEA BISSAU",
            "country_id": "0"
        },
        {
            "short_name": "GUY",
            "short_name_two": "GU",
            "name": "GUYANA",
            "country_id": "0"
        },
        {
            "short_name": "HTI",
            "short_name_two": "HT",
            "name": "HAITI",
            "country_id": "0"
        },
        {
            "short_name": "HMD",
            "short_name_two": "HM",
            "name": "HEARD AND MCDONALD ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "VAT",
            "short_name_two": "VA",
            "name": "HOLY SEE (VATICAN CITY STATE)",
            "country_id": "0"
        },
        {
            "short_name": "HND",
            "short_name_two": "HN",
            "name": "HONDURAS",
            "country_id": "0"
        },
        {
            "short_name": "HKG",
            "short_name_two": "HK",
            "name": "HONG KONG",
            "country_id": "0"
        },
        {
            "short_name": "HUN",
            "short_name_two": "HU",
            "name": "HUNGURY",
            "country_id": "0"
        },
        {
            "short_name": "ISL",
            "short_name_two": "IS",
            "name": "ICELAND",
            "country_id": "0"
        },
        {
            "short_name": "IND",
            "short_name_two": "IN",
            "name": "INDIA",
            "country_id": "0"
        },
        {
            "short_name": "IDN",
            "short_name_two": "ID",
            "name": "INDONESIA",
            "country_id": "0"
        },
        {
            "short_name": "IRN",
            "short_name_two": "IR",
            "name": "IRAN",
            "country_id": "0"
        },
        {
            "short_name": "IRQ",
            "short_name_two": "IR",
            "name": "IRAQ",
            "country_id": "0"
        },
        {
            "short_name": "IRL",
            "short_name_two": "IR",
            "name": "IRELAND",
            "country_id": "0"
        },
        {
            "short_name": "IMN",
            "short_name_two": "IM",
            "name": "ISLE OF MAN",
            "country_id": "0"
        },
        {
            "short_name": "ISR",
            "short_name_two": "IS",
            "name": "ISRAEL",
            "country_id": "0"
        },
        {
            "short_name": "ITA",
            "short_name_two": "IT",
            "name": "ITALY",
            "country_id": "0"
        },
        {
            "short_name": "JAM",
            "short_name_two": "JA",
            "name": "JAMAICA",
            "country_id": "0"
        },
        {
            "short_name": "JPN",
            "short_name_two": "JP",
            "name": "JAPAN",
            "country_id": "0"
        },
        {
            "short_name": "JEY",
            "short_name_two": "JE",
            "name": "JERSEY",
            "country_id": "0"
        },
        {
            "short_name": "JOR",
            "short_name_two": "JO",
            "name": "JORDAN",
            "country_id": "0"
        },
        {
            "short_name": "KAZ",
            "short_name_two": "KA",
            "name": "KAZAKHSITAN",
            "country_id": "0"
        },
        {
            "short_name": "KEN",
            "short_name_two": "KE",
            "name": "KENYA",
            "country_id": "0"
        },
        {
            "short_name": "KIR",
            "short_name_two": "KI",
            "name": "KIRIBATI",
            "country_id": "0"
        },
        {
            "short_name": "PRK",
            "short_name_two": "PR",
            "name": "KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF",
            "country_id": "0"
        },
        {
            "short_name": "KOR",
            "short_name_two": "KO",
            "name": "KOREA, REPUBLIC OF",
            "country_id": "0"
        },
        {
            "short_name": "KWT",
            "short_name_two": "KW",
            "name": "KUWAIT",
            "country_id": "0"
        },
        {
            "short_name": "KGZ",
            "short_name_two": "KG",
            "name": "KYRGYZSTAN",
            "country_id": "0"
        },
        {
            "short_name": "LAO",
            "short_name_two": "LA",
            "name": "LAOS",
            "country_id": "0"
        },
        {
            "short_name": "LVA",
            "short_name_two": "LV",
            "name": "LATVIA",
            "country_id": "0"
        },
        {
            "short_name": "LBN",
            "short_name_two": "LB",
            "name": "LEBANON",
            "country_id": "0"
        },
        {
            "short_name": "LSO",
            "short_name_two": "LS",
            "name": "LESOTHO",
            "country_id": "0"
        },
        {
            "short_name": "LBR",
            "short_name_two": "LB",
            "name": "LIBERIA",
            "country_id": "0"
        },
        {
            "short_name": "LBY",
            "short_name_two": "LB",
            "name": "LIBYA",
            "country_id": "0"
        },
        {
            "short_name": "LIE",
            "short_name_two": "LI",
            "name": "LIECHTENSTEIN",
            "country_id": "0"
        },
        {
            "short_name": "LTU",
            "short_name_two": "LT",
            "name": "LITHUANIA",
            "country_id": "0"
        },
        {
            "short_name": "LUX",
            "short_name_two": "LU",
            "name": "LUXEMBOURG",
            "country_id": "0"
        },
        {
            "short_name": "MAC",
            "short_name_two": "MA",
            "name": "MACAU",
            "country_id": "0"
        },
        {
            "short_name": "MKD",
            "short_name_two": "MK",
            "name": "MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF",
            "country_id": "0"
        },
        {
            "short_name": "MDG",
            "short_name_two": "MD",
            "name": "MADAGASCAR",
            "country_id": "0"
        },
        {
            "short_name": "MWI",
            "short_name_two": "MW",
            "name": "MALAWI",
            "country_id": "0"
        },
        {
            "short_name": "MYS",
            "short_name_two": "MY",
            "name": "MALAYSIA",
            "country_id": "0"
        },
        {
            "short_name": "MDV",
            "short_name_two": "MD",
            "name": "MALDIVES",
            "country_id": "0"
        },
        {
            "short_name": "MLI",
            "short_name_two": "ML",
            "name": "MALI",
            "country_id": "0"
        },
        {
            "short_name": "MLT",
            "short_name_two": "ML",
            "name": "MALTA",
            "country_id": "0"
        },
        {
            "short_name": "MHL",
            "short_name_two": "MH",
            "name": "MARSHALL ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "MTQ",
            "short_name_two": "MT",
            "name": "MARTINIQUE",
            "country_id": "0"
        },
        {
            "short_name": "MRT",
            "short_name_two": "MR",
            "name": "MAURITANIA",
            "country_id": "0"
        },
        {
            "short_name": "MUS",
            "short_name_two": "MU",
            "name": "MAURITIUS",
            "country_id": "0"
        },
        {
            "short_name": "MYT",
            "short_name_two": "MY",
            "name": "MAYOTTE",
            "country_id": "0"
        },
        {
            "short_name": "MEX",
            "short_name_two": "ME",
            "name": "MEXICO",
            "country_id": "0"
        },
        {
            "short_name": "FSM",
            "short_name_two": "FS",
            "name": "MICRONESIA, FEDERATED STATES OF",
            "country_id": "0"
        },
        {
            "short_name": "MDA",
            "short_name_two": "MD",
            "name": "MOLDOVA, REPUBLIC OF",
            "country_id": "0"
        },
        {
            "short_name": "MCO",
            "short_name_two": "MC",
            "name": "MONACO",
            "country_id": "0"
        },
        {
            "short_name": "MNG",
            "short_name_two": "MN",
            "name": "MONGOLIA",
            "country_id": "0"
        },
        {
            "short_name": "MNE",
            "short_name_two": "MN",
            "name": "MONTENEGRO",
            "country_id": "0"
        },
        {
            "short_name": "MSR",
            "short_name_two": "MS",
            "name": "MONTSERRAT",
            "country_id": "0"
        },
        {
            "short_name": "MAR",
            "short_name_two": "MA",
            "name": "MOROCCO",
            "country_id": "0"
        },
        {
            "short_name": "MOZ",
            "short_name_two": "MO",
            "name": "MOZAMBIQUE",
            "country_id": "0"
        },
        {
            "short_name": "MMR",
            "short_name_two": "MM",
            "name": "MYANMAR (BURMA)",
            "country_id": "0"
        },
        {
            "short_name": "NAM",
            "short_name_two": "NA",
            "name": "NAMIBIA",
            "country_id": "0"
        },
        {
            "short_name": "NRU",
            "short_name_two": "NR",
            "name": "NAURU",
            "country_id": "0"
        },
        {
            "short_name": "NPL",
            "short_name_two": "NP",
            "name": "NEPAL",
            "country_id": "0"
        },
        {
            "short_name": "NLD",
            "short_name_two": "NL",
            "name": "NETHERLANDS",
            "country_id": "0"
        },
        {
            "short_name": "ANT",
            "short_name_two": "AN",
            "name": "NETHERLANDS ANTILLES",
            "country_id": "0"
        },
        {
            "short_name": "NTZ",
            "short_name_two": "NT",
            "name": "NEUTRAL ZONE",
            "country_id": "0"
        },
        {
            "short_name": "NCL",
            "short_name_two": "NC",
            "name": "NEW CALEDONIA",
            "country_id": "0"
        },
        {
            "short_name": "NZL",
            "short_name_two": "NZ",
            "name": "NEW ZEALAND",
            "country_id": "0"
        },
        {
            "short_name": "NIC",
            "short_name_two": "NI",
            "name": "NICARAGUA",
            "country_id": "0"
        },
        {
            "short_name": "NER",
            "short_name_two": "NE",
            "name": "NIGER",
            "country_id": "0"
        },
        {
            "short_name": "NGA",
            "short_name_two": "NG",
            "name": "NIGERIA",
            "country_id": "0"
        },
        {
            "short_name": "NIU",
            "short_name_two": "NI",
            "name": "NIUE",
            "country_id": "0"
        },
        {
            "short_name": "NFK",
            "short_name_two": "NF",
            "name": "NORFOLK ISLAND",
            "country_id": "0"
        },
        {
            "short_name": "MNP",
            "short_name_two": "MN",
            "name": "NORTHERN MARIANA ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "NOR",
            "short_name_two": "NO",
            "name": "NORWAY",
            "country_id": "0"
        },
        {
            "short_name": "OMN",
            "short_name_two": "OM",
            "name": "OMAN",
            "country_id": "0"
        },
        {
            "short_name": "OTH",
            "short_name_two": "OT",
            "name": "OTHERS",
            "country_id": "0"
        },
        {
            "short_name": "PAK",
            "short_name_two": "PA",
            "name": "PAKISTAN",
            "country_id": "0"
        },
        {
            "short_name": "PLW",
            "short_name_two": "PL",
            "name": "PALAU",
            "country_id": "0"
        },
        {
            "short_name": "PSE",
            "short_name_two": "PS",
            "name": "PALESTINE",
            "country_id": "0"
        },
        {
            "short_name": "PAN",
            "short_name_two": "PA",
            "name": "PANAMA",
            "country_id": "0"
        },
        {
            "short_name": "PNG",
            "short_name_two": "PN",
            "name": "PAPUA NEW GUINEA",
            "country_id": "0"
        },
        {
            "short_name": "PRY",
            "short_name_two": "PR",
            "name": "PARAGUAY",
            "country_id": "0"
        },
        {
            "short_name": "PER",
            "short_name_two": "PE",
            "name": "PERU",
            "country_id": "0"
        },
        {
            "short_name": "PHL",
            "short_name_two": "PH",
            "name": "PHILLIPPINES",
            "country_id": "0"
        },
        {
            "short_name": "PCN",
            "short_name_two": "PC",
            "name": "PITCAIRN",
            "country_id": "0"
        },
        {
            "short_name": "POL",
            "short_name_two": "PO",
            "name": "POLAND",
            "country_id": "0"
        },
        {
            "short_name": "PRT",
            "short_name_two": "PR",
            "name": "PORTUGAL",
            "country_id": "0"
        },
        {
            "short_name": "XXX",
            "short_name_two": "XX",
            "name": "PRESON OF UNSPECIFIED NATIONALITY",
            "country_id": "0"
        },
        {
            "short_name": "PRI",
            "short_name_two": "PR",
            "name": "PUERPO RICO",
            "country_id": "0"
        },
        {
            "short_name": "QAT",
            "short_name_two": "QA",
            "name": "QATAR",
            "country_id": "0"
        },
        {
            "short_name": "XXB",
            "short_name_two": "XX",
            "name": "REFUGEE",
            "country_id": "0"
        },
        {
            "short_name": "XXC",
            "short_name_two": "XX",
            "name": "REFUGEE (NON-CONVENTIONAL) OTHER THAN XXB",
            "country_id": "0"
        },
        {
            "short_name": "UNK",
            "short_name_two": "UN",
            "name": "RESIDENT OF KOSOVO (UNMIK)",
            "country_id": "0"
        },
        {
            "short_name": "REU",
            "short_name_two": "RE",
            "name": "REUNION ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "ROU",
            "short_name_two": "RO",
            "name": "ROMANIA",
            "country_id": "0"
        },
        {
            "short_name": "RUS",
            "short_name_two": "RU",
            "name": "RUSSIAN FEDERATION",
            "country_id": "0"
        },
        {
            "short_name": "RWA",
            "short_name_two": "RW",
            "name": "RWANDA",
            "country_id": "0"
        },
        {
            "short_name": "BLM",
            "short_name_two": "BL",
            "name": "SAINT BARTHELEMY",
            "country_id": "0"
        },
        {
            "short_name": "SHN",
            "short_name_two": "SH",
            "name": "SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA",
            "country_id": "0"
        },
        {
            "short_name": "KNA",
            "short_name_two": "KN",
            "name": "SAINT KITTS AND NEVIS",
            "country_id": "0"
        },
        {
            "short_name": "LCA",
            "short_name_two": "LC",
            "name": "SAINT LUCIA",
            "country_id": "0"
        },
        {
            "short_name": "MAF",
            "short_name_two": "MA",
            "name": "SAINT MARTIN (FRENCH PART)",
            "country_id": "0"
        },
        {
            "short_name": "SPM",
            "short_name_two": "SP",
            "name": "SAINT PIERRE AND MIQUELON",
            "country_id": "0"
        },
        {
            "short_name": "VCT",
            "short_name_two": "VC",
            "name": "SAINT VINCENT AND THE GRENADINES",
            "country_id": "0"
        },
        {
            "short_name": "WSM",
            "short_name_two": "WS",
            "name": "SAMOA",
            "country_id": "0"
        },
        {
            "short_name": "SMR",
            "short_name_two": "SM",
            "name": "SAN MARINO",
            "country_id": "0"
        },
        {
            "short_name": "STP",
            "short_name_two": "ST",
            "name": "SAO TOME AND PRINCIP",
            "country_id": "0"
        },
        {
            "short_name": "SAU",
            "short_name_two": "SA",
            "name": "SAUDI ARABIA",
            "country_id": "0"
        },
        {
            "short_name": "SEN",
            "short_name_two": "SE",
            "name": "SENEGAL",
            "country_id": "0"
        },
        {
            "short_name": "SRB",
            "short_name_two": "SR",
            "name": "SERBIA",
            "country_id": "0"
        },
        {
            "short_name": "SCG",
            "short_name_two": "SC",
            "name": "SERBIA AND MONTENEGRO",
            "country_id": "0"
        },
        {
            "short_name": "SYC",
            "short_name_two": "SY",
            "name": "SEYCHELLES",
            "country_id": "0"
        },
        {
            "short_name": "SLE",
            "short_name_two": "SL",
            "name": "SIERRA LEONE",
            "country_id": "0"
        },
        {
            "short_name": "SGP",
            "short_name_two": "SG",
            "name": "SINGAPORE",
            "country_id": "0"
        },
        {
            "short_name": "SXM",
            "short_name_two": "SX",
            "name": "SINT MAARTEN (DUTCH PART)",
            "country_id": "0"
        },
        {
            "short_name": "SVK",
            "short_name_two": "SV",
            "name": "SLOVAK REPUBLIC",
            "country_id": "0"
        },
        {
            "short_name": "SVN",
            "short_name_two": "SV",
            "name": "SLOVENIA",
            "country_id": "0"
        },
        {
            "short_name": "SLB",
            "short_name_two": "SL",
            "name": "SOLOMON ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "SOM",
            "short_name_two": "SO",
            "name": "SOMALIA",
            "country_id": "0"
        },
        {
            "short_name": "ZAF",
            "short_name_two": "ZA",
            "name": "SOUTH AFRICA",
            "country_id": "0"
        },
        {
            "short_name": "SGS",
            "short_name_two": "SG",
            "name": "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "SSD",
            "short_name_two": "SS",
            "name": "SOUTH SUDAN",
            "country_id": "0"
        },
        {
            "short_name": "XOM",
            "short_name_two": "XO",
            "name": "SOVEREIGN MILITARY ORDER OF MALTA",
            "country_id": "0"
        },
        {
            "short_name": "ESP",
            "short_name_two": "ES",
            "name": "SPAIN",
            "country_id": "0"
        },
        {
            "short_name": "LKA",
            "short_name_two": "LK",
            "name": "SRI LANKA",
            "country_id": "0"
        },
        {
            "short_name": "XXA",
            "short_name_two": "XX",
            "name": "STATELESS PERSON",
            "country_id": "0"
        },
        {
            "short_name": "SDN",
            "short_name_two": "SD",
            "name": "SUDAN",
            "country_id": "0"
        },
        {
            "short_name": "SUR",
            "short_name_two": "SU",
            "name": "SURINAME",
            "country_id": "0"
        },
        {
            "short_name": "SJM",
            "short_name_two": "SJ",
            "name": "SVALBARD AND JAN MAYEN ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "SWZ",
            "short_name_two": "SW",
            "name": "SWAZILAND",
            "country_id": "0"
        },
        {
            "short_name": "SWE",
            "short_name_two": "SW",
            "name": "SWEDEN",
            "country_id": "0"
        },
        {
            "short_name": "CHE",
            "short_name_two": "CH",
            "name": "SWITZERLAND",
            "country_id": "0"
        },
        {
            "short_name": "SYR",
            "short_name_two": "SY",
            "name": "SYRIA",
            "country_id": "0"
        },
        {
            "short_name": "TWN",
            "short_name_two": "TW",
            "name": "TAIWAN",
            "country_id": "0"
        },
        {
            "short_name": "TZA",
            "short_name_two": "TZ",
            "name": "TANZANIA",
            "country_id": "0"
        },
        {
            "short_name": "TJK",
            "short_name_two": "TJ",
            "name": "TAZIKISTAN",
            "country_id": "0"
        },
        {
            "short_name": "THA",
            "short_name_two": "TH",
            "name": "THAILAND",
            "country_id": "0"
        },
        {
            "short_name": "TBT",
            "short_name_two": "TB",
            "name": "TIBENTIAN ORIGIN",
            "country_id": "0"
        },
        {
            "short_name": "TGO",
            "short_name_two": "TG",
            "name": "TOGO",
            "country_id": "0"
        },
        {
            "short_name": "TKL",
            "short_name_two": "TK",
            "name": "TOKELAU",
            "country_id": "0"
        },
        {
            "short_name": "TON",
            "short_name_two": "TO",
            "name": "TONGA",
            "country_id": "0"
        },
        {
            "short_name": "TTO",
            "short_name_two": "TT",
            "name": "TRINIDAD AND TOBAGO ",
            "country_id": "0"
        },
        {
            "short_name": "TUN",
            "short_name_two": "TU",
            "name": "TUNISIA",
            "country_id": "0"
        },
        {
            "short_name": "TUR",
            "short_name_two": "TU",
            "name": "TURKEY",
            "country_id": "0"
        },
        {
            "short_name": "TKM",
            "short_name_two": "TK",
            "name": "TURKMENISTAN",
            "country_id": "0"
        },
        {
            "short_name": "TCA",
            "short_name_two": "TC",
            "name": "TURKS AND CAICOS ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "TUV",
            "short_name_two": "TU",
            "name": "TUVALU",
            "country_id": "0"
        },
        {
            "short_name": "UGA",
            "short_name_two": "UG",
            "name": "UGANDA",
            "country_id": "0"
        },
        {
            "short_name": "GBD",
            "short_name_two": "GB",
            "name": "UK BRITISH DEPENDENT TERRITORIES CITIZEN",
            "country_id": "0"
        },
        {
            "short_name": "GBO",
            "short_name_two": "GB",
            "name": "UK BRITISH OVERSEAS CITIZEN",
            "country_id": "0"
        },
        {
            "short_name": "GBN",
            "short_name_two": "GB",
            "name": "UK BRITISH OVERSEAS NATIONAL ",
            "country_id": "0"
        },
        {
            "short_name": "GBP",
            "short_name_two": "GB",
            "name": "UK BRITISH PROTECTED PERSON",
            "country_id": "0"
        },
        {
            "short_name": "GBS",
            "short_name_two": "GB",
            "name": "UK BRITISH SUBJECT",
            "country_id": "0"
        },
        {
            "short_name": "UKR",
            "short_name_two": "UK",
            "name": "UKRAINE",
            "country_id": "0"
        },
        {
            "short_name": "ARE",
            "short_name_two": "AR",
            "name": "UNITED ARAB EMIRATES",
            "country_id": "0"
        },
        {
            "short_name": "GBR",
            "short_name_two": "GB",
            "name": "UNITED KINGDOM",
            "country_id": "0"
        },
        {
            "short_name": "UNA",
            "short_name_two": "UN",
            "name": "UNITED NATIONS",
            "country_id": "0"
        },
        {
            "short_name": "UNO",
            "short_name_two": "UN",
            "name": "UNITED NATIONS ORGANIZATION",
            "country_id": "0"
        },
        {
            "short_name": "UMI",
            "short_name_two": "UM",
            "name": "UNITED STATES MINOR OUTLYING ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "USA",
            "short_name_two": "US",
            "name": "UNITED STATES OF AMERICA",
            "country_id": "0"
        },
        {
            "short_name": "URY",
            "short_name_two": "UR",
            "name": "URAGUAY",
            "country_id": "0"
        },
        {
            "short_name": "UZB",
            "short_name_two": "UZ",
            "name": "UZBEKISTAN",
            "country_id": "0"
        },
        {
            "short_name": "VUT",
            "short_name_two": "VU",
            "name": "VANUATU (NEW HEBRIDES)",
            "country_id": "0"
        },
        {
            "short_name": "VEN",
            "short_name_two": "VE",
            "name": "VENEZUELA",
            "country_id": "0"
        },
        {
            "short_name": "VNM",
            "short_name_two": "VN",
            "name": "VIETNAM",
            "country_id": "0"
        },
        {
            "short_name": "VGB",
            "short_name_two": "VG",
            "name": "VIRGIN ISLANDS (BRITISH)",
            "country_id": "0"
        },
        {
            "short_name": "VIR",
            "short_name_two": "VI",
            "name": "VIRGIN ISLANDS (US)",
            "country_id": "0"
        },
        {
            "short_name": "WLF",
            "short_name_two": "WL",
            "name": "WALLIS AND FUTUNA ISLANDS",
            "country_id": "0"
        },
        {
            "short_name": "ESH",
            "short_name_two": "ES",
            "name": "WESTERN SAHARA",
            "country_id": "0"
        },
        {
            "short_name": "YEM",
            "short_name_two": "YE",
            "name": "YEMEN",
            "country_id": "0"
        },
        {
            "short_name": "YUG",
            "short_name_two": "YU",
            "name": "YUGOSLAVIA",
            "country_id": "0"
        },
        {
            "short_name": "ZMB",
            "short_name_two": "ZM",
            "name": "ZAMBIA",
            "country_id": "0"
        },
        {
            "short_name": "ZWE",
            "short_name_two": "ZW",
            "name": "ZIMBABWE",
            "country_id": "0"
        }
    ],
    "visa_types": [
        {
          "value": "AYU",
          "text": "AYUSH VISA",
          "visa_id": 161
        },
        {
          "value": "BUS",
          "text": "BUSINESS VISA",
          "visa_id": 1
        },
        {
          "value": "CON",
          "text": "CONFERENCE VISA",
          "visa_id": 2
        },
        {
          "value": "DDV",
          "text": "DIPLOMATIC DEPENDENT VISA",
          "visa_id": 91
        },
        {
          "value": "DIP",
          "text": "DIPLOMATIC VISA",
          "visa_id": 3
        },
        {
          "value": "DEV",
          "text": "DOUBLE ENTRY VISA",
          "visa_id": 97
        },
        {
          "value": "EME",
          "text": "e-Emergency X-Misc Visa",
          "visa_id": 37
        },
        {
          "value": "EMP",
          "text": "EMPLOYMENT VISA",
          "visa_id": 4
        },
        {
          "value": "ENT",
          "text": "ENTRY VISA",
          "visa_id": 94
        },
        {
          "value": "EVI",
          "text": "e-VISA",
          "visa_id": 98
        },
        {
          "value": "FIL",
          "text": "FILMS VISA",
          "visa_id": 28
        },
        {
          "value": "JOU",
          "text": "JOURNALIST VISA",
          "visa_id": 6
        },
        {
          "value": "LTV",
          "text": "LONG TERM VISA",
          "visa_id": 7
        },
        {
          "value": "MED",
          "text": "MEDICAL VISA",
          "visa_id": 92
        },
        {
          "value": "MSP",
          "text": "MISC SP PERMISSION",
          "visa_id": 10
        },
        {
          "value": "MIS",
          "text": "MISSIONARY VISA",
          "visa_id": 11
        },
        {
          "value": "MTV",
          "text": "MOUNTAINEERING VISA",
          "visa_id": 93
        },
        {
          "value": "OCI",
          "text": "OCI",
          "visa_id": 22
        },
        {
          "value": "ODV",
          "text": "OFFICIAL DEPENDENT VISA",
          "visa_id": 90
        },
        {
          "value": "OFF",
          "text": "OFFICIAL VISA",
          "visa_id": 12
        },
        {
          "value": "PIO",
          "text": "PIO CARD HOLDER",
          "visa_id": 13
        },
        {
          "value": "PLG",
          "text": "PLIGRIM VISA",
          "visa_id": 96
        },
        {
          "value": "STU",
          "text": "STUDENT VISA",
          "visa_id": 16
        },
        {
          "value": "TLP",
          "text": "TEMPORARY LANDING PERMIT",
          "visa_id": 24
        },
        {
          "value": "TOU",
          "text": "TOURIST VISA",
          "visa_id": 17
        },
        {
          "value": "TRA",
          "text": "TRANSIT VISA",
          "visa_id": 18
        },
        {
          "value": "UND",
          "text": "UN DIPLOMAT VISA",
          "visa_id": 33
        },
        {
          "value": "UNN",
          "text": "UNITED NATION",
          "visa_id": 19
        },
        {
          "value": "UNO",
          "text": "UN OFFICIAL VISA",
          "visa_id": 34
        },
        {
          "value": "VOA",
          "text": "VISA-ON-ARRIVAL",
          "visa_id": 99
        },
        {
          "value": "VIS",
          "text": "VISITOR VISA",
          "visa_id": 95
        }
      ],
    "purpost_to_visit": [
        {
            "value": "18",
            "text": "accompanying parents"
        },
        {
            "value": "9",
            "text": "Accompanying Patient"
        },
        {
            "value": "10",
            "text": "Accompanying Patient as Doctor"
        },
        {
            "value": "17",
            "text": "accompanying Spouse"
        },
        {
            "value": "6",
            "text": "Business"
        },
        {
            "value": "12",
            "text": "Diplomatic"
        },
        {
            "value": "5",
            "text": "Education"
        },
        {
            "value": "13",
            "text": "Employment"
        },
        {
            "value": "19",
            "text": "Internship"
        },
        {
            "value": "2",
            "text": "Joining spouse"
        },
        {
            "value": "7",
            "text": "Journalism"
        },
        {
            "value": "8",
            "text": "Medical Treatement of self"
        },
        {
            "value": "1",
            "text": "Meeting friends/relatives"
        },
        {
            "value": "3",
            "text": "Minor child(either parent is indian)"
        },
        {
            "value": "11",
            "text": "Offical"
        },
        {
            "value": "15",
            "text": "Others"
        },
        {
            "value": "4",
            "text": "Seminar/conference. in India"
        },
        {
            "value": "14",
            "text": "Studies"
        },
        {
            "value": "16",
            "text": "Tourism"
        }
    ],
    "document_types": [
        { name: "Passport", short_name: "P" },
        { name: "Visa", short_name: "V" },
        { name: "Driving license", short_name: "DL" },
        { name: "Aadhar", short_name: "AC" },
        { name: "Pan Card", short_name: "PC" },
        { name: "Voter ID", short_name: "VI" },
        { name: "Others", short_name: "O" }
      ]
};