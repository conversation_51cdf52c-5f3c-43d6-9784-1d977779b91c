var sql = require('../config/db.js');
var table = require('../config/tables');
var moment = require('moment');
let currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss');
var constant = require('../config/constant');
module.exports = {
  create: async function (request, result) {
    let adminId = request.userDetails.user_type === constant.USER_TYPE.ADMIN ? request.userDetails.user_id:request.userDetails.admin_id;

    let query = 'INSERT INTO `' + table.visaType + '` (`admin_id`,`name`,`short_name`,`created_by`,`created_at`,`is_active`) VALUES(?,?,?,?,?,?)';
    try {
      await sql.query(query, [adminId, request.name,request.shortName,request.userDetails.user_id, currentDateTime,request.is_active]);
      result(null, { status: true, msg: "successfully created" });
    } catch (error) {
      console.log(error);
      result(error, null);
    }
  },
  update: async function (id, request, result) {
    let query =
      "UPDATE `" +
      table.visaType +
      "` SET `is_active`=?,`name`=?,`short_name`=?,`modified_by`=?,`modified_at`=? WHERE `id`=?";
    try {
      let row = await sql.query(query, [
        request.is_active,
        request.name,
        request.shortName,
        request.userDetails.user_id,
        currentDateTime,
        id
      ]);
      result(null, {
        status: row.affectedRows > 0 ? true : false,
        changes: row.changedRows > 0 ? true : false,
        msg:
          row.affectedRows > 0
            ? row.changedRows > 0
              ? "Successfully updated"
              : "No changes updated details, try again"
            : "Update failed, try again",
      });
    } catch (error) {
      console.log(error);
      result(error, null);
    }
  },
  getAll: async function (offset, noOfRecordsPerPage, search, userDetails, result) {
    let whereSearch = search ? ("AND ( `name` LIKE '%" +
      search +
      "%' ESCAPE '!' OR `short_name` LIKE '%" +
      search +
      "%' ESCAPE '!')") : '';

    let query =
      "SELECT * FROM `" +
      table.visaType +
      "` WHERE `admin_id`=? AND `status`=?" + whereSearch;

      let adminId = userDetails.user_type === constant.USER_TYPE.ADMIN ? userDetails.user_id:userDetails.admin_id;

    try {
      let rowCount = await sql.query(query, [adminId, constant.STATUS.LIVE]);
      let totalRows = rowCount.length;
      let totalPages = Math.ceil(totalRows / noOfRecordsPerPage);
      result(null, {
        status: true,
        totalPages: totalPages,
        data: await getPageOffsetData(offset, noOfRecordsPerPage, whereSearch, adminId, result),
      });
    } catch (error) {
      console.log(error);
      result(error, null);
    }
  },
  getById: async function (id, result) {
    let query = 'SELECT *,DATE_FORMAT(`created_at`,"%d-%m-%Y") as created_at  FROM `' + table.visaType + '` WHERE `status`=? AND `id`=?';

    try {
      let row = await sql.query(query, [constant.STATUS.LIVE, id]);
      result(null, { status: true, data: row[0] });
    } catch (error) {
      console.log(error);
      result(error, null);
    }
  },
  deleteById: async function (id, result) {
    let query =
      "UPDATE `" + table.visaType + '` SET `status`=\''+constant.STATUS.DELETED+'\'  WHERE id=?';
    try {
      let row = await sql.query(query, [id]);

      result(null, {
        status: row.affectedRows > 0 ? true : false,
        changes: row.changedRows > 0 ? true : false,
        msg:
          row.affectedRows > 0
            ? row.changedRows > 0
              ? "Successfully deleted"
              : "Not deleted, try again"
            : "Update failed, try again",
      });
    } catch (error) {
      console.log(error);
      result(error, null);
    }
  }, getvisaTypeList: async function (req, result) {
    let query = 'SELECT `id`,`name`,`short_name` FROM `' + table.visaType + '` WHERE `admin_id`=? AND `status`=? ORDER BY `name` ASC';

    let adminId = req.userDetails.user_type === constant.USER_TYPE.ADMIN ? req.userDetails.user_id:req.userDetails.admin_id;

    try {
      let res = await sql.query(query, [adminId, constant.STATUS.LIVE]);
      result(null, {
        status: true,
        data: res.length ? res : []
      });
    } catch (error) {
      console.log(error);
      result(error, null);
    }
  }
}
async function getPageOffsetData(offset, noOfRecordsPerPage, whereSearch, adminId, result) {
  let query = 'SELECT *,DATE_FORMAT(`created_at`,"%d-%m-%Y") as created_at  FROM `' + table.visaType + '` WHERE `admin_id`=? AND `status`=? ' + whereSearch + 'ORDER BY `id` DESC LIMIT ?,?';
  try {
    let res = await sql.query(query, [adminId, constant.STATUS.LIVE, offset, noOfRecordsPerPage]);
    return res.length ? res : [];
  } catch (error) {
    console.log(error);
    result(error, null);
  }
}